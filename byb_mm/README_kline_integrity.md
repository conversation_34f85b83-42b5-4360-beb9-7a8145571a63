# K线数据完整性检查和智能填补系统

## 概述

这个系统提供了完整的K线数据完整性检查和智能填补功能，能够：

1. **检测数据问题**：缺失数据、重复时间戳、不规则时间间隔
2. **智能填补缺失值**：支持多种填补方法
3. **验证数据质量**：确保填补后的数据符合预期
4. **生成详细报告**：提供完整的分析和建议

## 文件说明

### 核心文件

- `kline_integrity_checker.py` - 独立的命令行工具
- `kline_restore.py` - 集成了完整性检查的K线修复系统
- `example_integrity_check.py` - 使用示例和演示

### 功能模块

1. **完整性检查** (`check_kline_integrity`)
   - 检查时间戳连续性
   - 验证时间间隔一致性
   - 检测重复和缺失数据

2. **智能填补** (`intelligent_fill_missing_data`)
   - 线性插值法 (interpolation)
   - 前向填充法 (forward_fill)
   - 后向填充法 (backward_fill)
   - 平均值填充法 (average)

3. **数据验证** (`validate_filled_data`)
   - 时间连续性检查
   - 价格连续性检查
   - 交易量合理性检查

4. **报告生成** (`generate_integrity_report`)
   - 详细的完整性分析
   - 填补结果统计
   - 问题诊断和建议

## 使用方法

### 1. 命令行工具使用

#### 基本检查（仅检查，不填补）
```bash
python kline_integrity_checker.py your_kline_data.csv --check-only
```

#### 检查并填补缺失数据
```bash
# 使用默认的插值法填补
python kline_integrity_checker.py your_kline_data.csv

# 指定填补方法
python kline_integrity_checker.py your_kline_data.csv --fill-method interpolation
python kline_integrity_checker.py your_kline_data.csv --fill-method forward_fill
python kline_integrity_checker.py your_kline_data.csv --fill-method backward_fill
python kline_integrity_checker.py your_kline_data.csv --fill-method average
```

#### 指定时间间隔和输出文件
```bash
# 检查5分钟K线数据（300秒间隔）
python kline_integrity_checker.py your_5min_kline.csv --interval 300

# 指定输出文件名
python kline_integrity_checker.py your_kline_data.csv --output fixed_kline_data.csv
```

### 2. Python代码中使用

```python
from kline_integrity_checker import (
    check_kline_integrity,
    intelligent_fill_missing_data,
    remove_duplicate_timestamps,
    validate_filled_data,
    generate_integrity_report
)
import pandas as pd

# 读取K线数据
df = pd.read_csv('your_kline_data.csv')

# 移除重复时间戳
df_dedup = remove_duplicate_timestamps(df)

# 检查完整性
integrity_result = check_kline_integrity(df_dedup, expected_interval_seconds=60)

# 智能填补缺失数据
if integrity_result['missing_count'] > 0:
    df_filled = intelligent_fill_missing_data(df_dedup, integrity_result, fill_method='interpolation')
    
    # 验证填补结果
    validation_result = validate_filled_data(df_dedup, df_filled)
    
    # 生成报告
    generate_integrity_report(integrity_result, validation_result, 'integrity_report.txt')
    
    # 保存填补后的数据
    df_filled.to_csv('filled_kline_data.csv', index=False)
```

### 3. 集成到K线修复系统

在 `kline_restore.py` 的主函数中，完整性检查已经集成到处理流程中：

```python
# 运行完整的K线处理系统（包含完整性检查）
python kline_restore.py
```

## 数据格式要求

输入的CSV文件必须包含以下列：

- `timestamp_sec`: 时间戳（秒）
- `open`: 开盘价
- `high`: 最高价
- `low`: 最低价
- `close`: 收盘价
- `vol`: 交易量
- `amount`: 交易额

## 填补方法说明

### 1. 线性插值法 (interpolation) - 推荐
- **原理**：基于前后K线数据进行线性插值
- **优点**：保持价格趋势的连续性，最接近真实市场行为
- **适用**：大多数情况下的首选方法

### 2. 前向填充法 (forward_fill)
- **原理**：使用前一根K线的收盘价作为缺失K线的所有价格
- **优点**：保守稳定，不会产生异常波动
- **适用**：市场相对平稳的时期

### 3. 后向填充法 (backward_fill)
- **原理**：使用后一根K线的开盘价作为缺失K线的所有价格
- **优点**：与后续价格保持一致
- **适用**：需要与后续趋势对齐的情况

### 4. 平均值填充法 (average)
- **原理**：使用前后K线价格的平均值
- **优点**：平滑过渡，减少价格跳跃
- **适用**：价格波动较大的情况

## 输出文件

### 1. 填补后的数据文件
- 文件名：`原文件名_filled.csv`
- 内容：完整的K线数据，包含填补的记录

### 2. 完整性报告
- 文件名：`原文件名_integrity_report.txt`
- 内容：
  - 原始数据统计
  - 完整性检查结果
  - 缺失数据详情
  - 填补结果验证
  - 问题诊断和建议

## 示例演示

运行示例演示：

```bash
python example_integrity_check.py
```

这将：
1. 创建包含各种数据问题的示例K线数据
2. 演示完整性检查过程
3. 展示不同填补方法的效果
4. 生成完整的分析报告

## 注意事项

1. **数据备份**：在处理重要数据前，请先备份原始文件
2. **时间间隔**：确保指定正确的时间间隔（1分钟=60秒，5分钟=300秒）
3. **数据质量**：填补只能解决缺失问题，无法修复错误的价格数据
4. **验证结果**：填补后请检查验证报告，确认数据质量符合要求

## 常见问题

### Q: 为什么填补后仍有不规则时间间隔？
A: 可能存在人为添加的不规则记录，这些需要手动检查和处理。

### Q: 价格连续性检查未通过怎么办？
A: 这通常是由于原始数据中存在异常价格跳跃，可以结合价格平滑功能处理。

### Q: 如何处理交易量为零的记录？
A: 填补的缺失记录可能会有零交易量，这是正常的，表示该时间段无交易。

### Q: 可以处理其他时间周期的K线吗？
A: 可以，通过 `--interval` 参数指定相应的秒数即可。

## 技术支持

如有问题或建议，请查看代码注释或联系开发团队。
