#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线数据完整性检查使用示例
演示如何使用kline_integrity_checker.py检查和修复K线数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from kline_integrity_checker import (
    check_kline_integrity, 
    intelligent_fill_missing_data, 
    remove_duplicate_timestamps,
    validate_filled_data,
    generate_integrity_report
)

def create_sample_kline_data_with_gaps():
    """
    创建一个包含缺失数据的示例K线数据集
    用于演示完整性检查功能
    """
    print("正在创建示例K线数据（包含缺失和重复数据）...")
    
    # 创建基础时间序列（1分钟间隔）
    start_time = datetime(2025, 7, 4, 9, 0, 0)  # 9:00开始
    end_time = datetime(2025, 7, 4, 16, 0, 0)   # 16:00结束
    
    # 生成完整的时间序列
    time_range = pd.date_range(start=start_time, end=end_time, freq='1T')
    timestamps = [int(t.timestamp()) for t in time_range]
    
    # 生成基础价格数据
    base_price = 0.001234
    price_data = []
    
    for i, ts in enumerate(timestamps):
        # 模拟价格波动
        price_change = np.random.normal(0, 0.00001)  # 小幅随机波动
        current_price = base_price + price_change * i * 0.1
        
        # 生成OHLC数据
        open_price = current_price
        close_price = current_price + np.random.normal(0, 0.000001)
        high_price = max(open_price, close_price) + abs(np.random.normal(0, 0.000001))
        low_price = min(open_price, close_price) - abs(np.random.normal(0, 0.000001))
        
        # 生成交易量数据
        volume = np.random.uniform(100, 1000)
        amount = volume * current_price
        
        price_data.append({
            'timestamp_sec': ts,
            'open': round(open_price, 6),
            'high': round(high_price, 6),
            'low': round(low_price, 6),
            'close': round(close_price, 6),
            'vol': round(volume, 2),
            'amount': round(amount, 6)
        })
    
    df = pd.DataFrame(price_data)
    
    # 人为制造一些数据问题用于演示
    print("正在制造数据问题用于演示...")
    
    # 1. 移除一些记录（制造缺失）
    missing_indices = [50, 51, 52, 100, 101, 150, 200, 201, 202, 203]  # 移除一些连续的记录
    df_with_gaps = df.drop(missing_indices).reset_index(drop=True)
    print(f"移除了 {len(missing_indices)} 条记录，制造缺失数据")
    
    # 2. 复制一些记录（制造重复）
    duplicate_indices = [10, 20, 30]
    duplicate_records = df_with_gaps.iloc[duplicate_indices].copy()
    df_with_issues = pd.concat([df_with_gaps, duplicate_records], ignore_index=True)
    print(f"复制了 {len(duplicate_indices)} 条记录，制造重复数据")
    
    # 3. 添加一些不规则时间间隔
    irregular_record = df_with_issues.iloc[80].copy()
    irregular_record['timestamp_sec'] += 30  # 增加30秒，制造不规则间隔
    df_with_issues = pd.concat([df_with_issues, pd.DataFrame([irregular_record])], ignore_index=True)
    print("添加了1条不规则时间间隔记录")
    
    # 重新排序
    df_with_issues = df_with_issues.sort_values('timestamp_sec').reset_index(drop=True)
    
    return df_with_issues, df  # 返回有问题的数据和完整数据

def demonstrate_integrity_check():
    """演示完整性检查功能"""
    print("="*80)
    print("K线数据完整性检查演示")
    print("="*80)
    
    # 创建示例数据
    df_with_issues, df_complete = create_sample_kline_data_with_gaps()
    
    # 保存示例数据
    sample_file = 'sample_kline_with_issues.csv'
    df_with_issues.to_csv(sample_file, index=False)
    print(f"\n示例数据已保存到: {sample_file}")
    print(f"数据包含 {len(df_with_issues)} 条记录")
    
    # 第一步：检查完整性
    print("\n" + "="*60)
    print("第一步：检查数据完整性")
    print("="*60)
    
    integrity_result = check_kline_integrity(df_with_issues, expected_interval_seconds=60)
    
    # 第二步：移除重复数据
    print("\n" + "="*60)
    print("第二步：移除重复数据")
    print("="*60)
    
    df_dedup = remove_duplicate_timestamps(df_with_issues)
    
    # 第三步：智能填补缺失数据
    print("\n" + "="*60)
    print("第三步：智能填补缺失数据")
    print("="*60)
    
    # 重新检查去重后的数据
    integrity_result_dedup = check_kline_integrity(df_dedup, expected_interval_seconds=60)
    
    if integrity_result_dedup['missing_count'] > 0:
        # 演示不同的填补方法
        fill_methods = ['interpolation', 'forward_fill', 'backward_fill', 'average']
        
        for method in fill_methods:
            print(f"\n--- 使用 {method} 方法填补 ---")
            df_filled = intelligent_fill_missing_data(df_dedup, integrity_result_dedup, fill_method=method)
            
            # 验证填补结果
            validation_result = validate_filled_data(df_dedup, df_filled, expected_interval_seconds=60)
            
            # 保存填补后的数据
            output_file = f'sample_kline_filled_{method}.csv'
            df_filled.to_csv(output_file, index=False)
            print(f"填补后的数据已保存到: {output_file}")
    
    # 第四步：生成完整性报告
    print("\n" + "="*60)
    print("第四步：生成完整性报告")
    print("="*60)
    
    # 使用最佳方法（插值法）的结果
    df_final = intelligent_fill_missing_data(df_dedup, integrity_result_dedup, fill_method='interpolation')
    validation_final = validate_filled_data(df_dedup, df_final, expected_interval_seconds=60)
    
    report_file = 'sample_kline_integrity_report.txt'
    generate_integrity_report(integrity_result_dedup, validation_final, report_file)
    
    # 第五步：对比原始完整数据和修复后数据
    print("\n" + "="*60)
    print("第五步：对比分析")
    print("="*60)
    
    print(f"原始完整数据: {len(df_complete)} 条记录")
    print(f"有问题的数据: {len(df_with_issues)} 条记录")
    print(f"去重后数据: {len(df_dedup)} 条记录")
    print(f"修复后数据: {len(df_final)} 条记录")
    
    # 计算修复准确性
    if len(df_final) == len(df_complete):
        print("✓ 记录数量完全恢复")
        
        # 检查时间戳是否完全匹配
        original_timestamps = set(df_complete['timestamp_sec'])
        recovered_timestamps = set(df_final['timestamp_sec'])
        
        if original_timestamps == recovered_timestamps:
            print("✓ 时间戳完全恢复")
        else:
            missing_in_recovery = original_timestamps - recovered_timestamps
            extra_in_recovery = recovered_timestamps - original_timestamps
            print(f"⚠ 时间戳恢复不完全，缺失: {len(missing_in_recovery)}, 多余: {len(extra_in_recovery)}")
    else:
        print(f"⚠ 记录数量未完全恢复，差异: {len(df_complete) - len(df_final)}")
    
    # 清理示例文件
    print(f"\n演示完成！生成的文件:")
    files = [
        sample_file,
        'sample_kline_filled_interpolation.csv',
        'sample_kline_filled_forward_fill.csv', 
        'sample_kline_filled_backward_fill.csv',
        'sample_kline_filled_average.csv',
        report_file
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"  - {file}")
    
    print(f"\n可以使用以下命令行工具进行检查:")
    print(f"python kline_integrity_checker.py {sample_file}")
    print(f"python kline_integrity_checker.py {sample_file} --fill-method interpolation")
    print(f"python kline_integrity_checker.py {sample_file} --check-only")

def main():
    """主函数"""
    demonstrate_integrity_check()

if __name__ == "__main__":
    main()
