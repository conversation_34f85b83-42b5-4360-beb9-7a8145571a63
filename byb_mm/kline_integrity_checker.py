#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K线数据完整性检查和智能填补工具
功能：
1. 检查K线数据的时间戳连续性
2. 验证K线频率一致性
3. 检测重复和缺失数据
4. 智能填补缺失值
5. 生成详细的完整性报告
"""

import pandas as pd
import numpy as np
from datetime import datetime
import argparse
import os
import sys

def check_kline_integrity(df, expected_interval_seconds=60):
    """
    检查K线数据的完整性，包括时间戳连续性和频率验证
    Args:
        df: K线数据DataFrame，包含timestamp_sec列
        expected_interval_seconds: 期望的时间间隔（秒），默认60秒（1分钟）
    Returns:
        dict: 包含检查结果的字典
    """
    print(f"正在检查K线数据完整性...")
    print(f"期望时间间隔: {expected_interval_seconds}秒")
    
    # 确保数据按时间排序
    df_sorted = df.sort_values('timestamp_sec').reset_index(drop=True)
    
    # 检查结果
    integrity_result = {
        'total_records': len(df_sorted),
        'time_range': {
            'start': df_sorted['timestamp_sec'].iloc[0],
            'end': df_sorted['timestamp_sec'].iloc[-1],
            'start_datetime': datetime.fromtimestamp(df_sorted['timestamp_sec'].iloc[0]),
            'end_datetime': datetime.fromtimestamp(df_sorted['timestamp_sec'].iloc[-1])
        },
        'missing_intervals': [],
        'duplicate_timestamps': [],
        'irregular_intervals': [],
        'expected_total_records': 0,
        'missing_count': 0,
        'duplicate_count': 0,
        'irregular_count': 0
    }
    
    # 计算期望的总记录数
    total_duration = df_sorted['timestamp_sec'].iloc[-1] - df_sorted['timestamp_sec'].iloc[0]
    expected_total = int(total_duration / expected_interval_seconds) + 1
    integrity_result['expected_total_records'] = expected_total
    
    print(f"数据时间范围: {integrity_result['time_range']['start_datetime']} 到 {integrity_result['time_range']['end_datetime']}")
    print(f"实际记录数: {len(df_sorted)}")
    print(f"期望记录数: {expected_total}")
    
    # 检查重复时间戳
    timestamp_counts = df_sorted['timestamp_sec'].value_counts()
    duplicates = timestamp_counts[timestamp_counts > 1]
    if len(duplicates) > 0:
        integrity_result['duplicate_timestamps'] = duplicates.index.tolist()
        integrity_result['duplicate_count'] = len(duplicates)
        print(f"⚠ 发现 {len(duplicates)} 个重复时间戳")
    
    # 检查时间间隔
    time_diffs = df_sorted['timestamp_sec'].diff().dropna()
    
    # 统计时间间隔分布
    interval_counts = time_diffs.value_counts().sort_index()
    print(f"\n时间间隔分布:")
    for interval, count in interval_counts.head(10).items():
        print(f"  {int(interval)}秒: {count}次 ({count/len(time_diffs)*100:.1f}%)")
    
    # 检查缺失的时间间隔
    for i in range(1, len(df_sorted)):
        current_time = df_sorted['timestamp_sec'].iloc[i]
        prev_time = df_sorted['timestamp_sec'].iloc[i-1]
        time_diff = current_time - prev_time
        
        # 检查是否为期望间隔的倍数
        if time_diff > expected_interval_seconds:
            missing_intervals = int(time_diff / expected_interval_seconds) - 1
            if missing_intervals > 0:
                # 记录缺失的时间点
                for j in range(1, missing_intervals + 1):
                    missing_timestamp = prev_time + j * expected_interval_seconds
                    integrity_result['missing_intervals'].append({
                        'timestamp': missing_timestamp,
                        'datetime': datetime.fromtimestamp(missing_timestamp),
                        'after_index': i-1,
                        'gap_duration': time_diff
                    })
        
        # 检查不规则间隔
        if time_diff != expected_interval_seconds and time_diff > 0:
            integrity_result['irregular_intervals'].append({
                'index': i,
                'timestamp': current_time,
                'datetime': datetime.fromtimestamp(current_time),
                'interval': time_diff,
                'expected': expected_interval_seconds
            })
    
    integrity_result['missing_count'] = len(integrity_result['missing_intervals'])
    integrity_result['irregular_count'] = len(integrity_result['irregular_intervals'])
    
    # 输出检查结果
    print(f"\n=== 完整性检查结果 ===")
    print(f"缺失记录数: {integrity_result['missing_count']}")
    print(f"重复记录数: {integrity_result['duplicate_count']}")
    print(f"不规则间隔数: {integrity_result['irregular_count']}")
    
    if integrity_result['missing_count'] == 0 and integrity_result['duplicate_count'] == 0:
        print("✓ 数据完整性良好，无缺失或重复")
    else:
        print("⚠ 发现数据完整性问题，建议进行修复")
    
    return integrity_result

def intelligent_fill_missing_data(df, integrity_result, fill_method='interpolation'):
    """
    智能填补缺失的K线数据
    Args:
        df: 原始K线数据DataFrame
        integrity_result: 完整性检查结果
        fill_method: 填补方法 ('interpolation', 'forward_fill', 'backward_fill', 'average')
    Returns:
        填补后的DataFrame
    """
    if integrity_result['missing_count'] == 0:
        print("无需填补，数据完整")
        return df.copy()
    
    print(f"正在使用 {fill_method} 方法智能填补 {integrity_result['missing_count']} 个缺失数据点...")
    
    df_filled = df.copy()
    df_filled = df_filled.sort_values('timestamp_sec').reset_index(drop=True)
    
    # 为每个缺失点创建新记录
    new_records = []
    
    for missing_info in integrity_result['missing_intervals']:
        missing_timestamp = missing_info['timestamp']
        after_index = missing_info['after_index']
        
        # 获取前后的K线数据用于插值
        if after_index >= 0 and after_index + 1 < len(df_filled):
            prev_record = df_filled.iloc[after_index]
            next_record = df_filled.iloc[after_index + 1]
            
            # 根据填补方法生成新记录
            if fill_method == 'interpolation':
                # 线性插值
                time_ratio = (missing_timestamp - prev_record['timestamp_sec']) / (next_record['timestamp_sec'] - prev_record['timestamp_sec'])
                
                new_record = {
                    'timestamp_sec': missing_timestamp,
                    'open': prev_record['close'],  # 开盘价使用前一根K线的收盘价
                    'close': prev_record['close'] + (next_record['open'] - prev_record['close']) * time_ratio,
                    'high': max(prev_record['close'], prev_record['close'] + (next_record['open'] - prev_record['close']) * time_ratio),
                    'low': min(prev_record['close'], prev_record['close'] + (next_record['open'] - prev_record['close']) * time_ratio),
                    'amount': (prev_record['amount'] + next_record['amount']) / 2,  # 交易额取平均
                    'vol': (prev_record['vol'] + next_record['vol']) / 2  # 交易量取平均
                }
                
            elif fill_method == 'forward_fill':
                # 前向填充
                new_record = {
                    'timestamp_sec': missing_timestamp,
                    'open': prev_record['close'],
                    'close': prev_record['close'],
                    'high': prev_record['close'],
                    'low': prev_record['close'],
                    'amount': 0,  # 缺失期间无交易
                    'vol': 0
                }
                
            elif fill_method == 'backward_fill':
                # 后向填充
                new_record = {
                    'timestamp_sec': missing_timestamp,
                    'open': next_record['open'],
                    'close': next_record['open'],
                    'high': next_record['open'],
                    'low': next_record['open'],
                    'amount': 0,
                    'vol': 0
                }
                
            elif fill_method == 'average':
                # 平均值填充
                avg_price = (prev_record['close'] + next_record['open']) / 2
                new_record = {
                    'timestamp_sec': missing_timestamp,
                    'open': avg_price,
                    'close': avg_price,
                    'high': avg_price,
                    'low': avg_price,
                    'amount': (prev_record['amount'] + next_record['amount']) / 2,
                    'vol': (prev_record['vol'] + next_record['vol']) / 2
                }
            
            # 确保OHLC逻辑正确
            new_record['high'] = max(new_record['open'], new_record['close'], new_record['high'])
            new_record['low'] = min(new_record['open'], new_record['close'], new_record['low'])
            
            new_records.append(new_record)
    
    # 将新记录添加到DataFrame
    if new_records:
        new_df = pd.DataFrame(new_records)
        df_filled = pd.concat([df_filled, new_df], ignore_index=True)
        df_filled = df_filled.sort_values('timestamp_sec').reset_index(drop=True)
        
        # 格式化数值
        for col in ['amount', 'vol', 'open', 'close', 'high', 'low']:
            df_filled[col] = df_filled[col].round(6)
    
    print(f"✓ 成功填补 {len(new_records)} 个缺失数据点")
    print(f"填补后数据总数: {len(df_filled)} 条")
    
    return df_filled

def remove_duplicate_timestamps(df):
    """
    移除重复时间戳的记录，保留交易量最大的记录
    Args:
        df: K线数据DataFrame
    Returns:
        去重后的DataFrame
    """
    print("正在移除重复时间戳...")
    
    original_count = len(df)
    
    # 按时间戳分组，保留交易量最大的记录
    df_dedup = df.loc[df.groupby('timestamp_sec')['vol'].idxmax()].reset_index(drop=True)
    
    removed_count = original_count - len(df_dedup)
    
    if removed_count > 0:
        print(f"✓ 移除了 {removed_count} 个重复时间戳记录")
    else:
        print("✓ 无重复时间戳")
    
    return df_dedup

def validate_filled_data(df_original, df_filled, expected_interval_seconds=60):
    """
    验证填补后的数据质量
    Args:
        df_original: 原始数据
        df_filled: 填补后的数据
        expected_interval_seconds: 期望时间间隔
    Returns:
        验证结果字典
    """
    print("正在验证填补后的数据质量...")
    
    validation_result = {
        'original_count': len(df_original),
        'filled_count': len(df_filled),
        'added_count': len(df_filled) - len(df_original),
        'time_continuity_check': True,
        'price_continuity_check': True,
        'volume_reasonableness_check': True,
        'issues': []
    }
    
    # 检查时间连续性
    time_diffs = df_filled['timestamp_sec'].diff().dropna()
    irregular_intervals = time_diffs[time_diffs != expected_interval_seconds]
    
    if len(irregular_intervals) > 0:
        validation_result['time_continuity_check'] = False
        validation_result['issues'].append(f"仍有 {len(irregular_intervals)} 个不规则时间间隔")
    
    # 检查价格连续性（避免异常跳跃）
    price_changes = df_filled['close'].pct_change().dropna()
    large_changes = price_changes[abs(price_changes) > 0.1]  # 超过10%的价格变化
    
    if len(large_changes) > 0:
        validation_result['price_continuity_check'] = False
        validation_result['issues'].append(f"发现 {len(large_changes)} 个异常价格跳跃（>10%）")
    
    # 检查交易量合理性
    zero_volume_count = (df_filled['vol'] == 0).sum()
    if zero_volume_count > validation_result['added_count'] * 1.2:  # 允许一些零交易量
        validation_result['volume_reasonableness_check'] = False
        validation_result['issues'].append(f"零交易量记录过多: {zero_volume_count}")
    
    # 输出验证结果
    print(f"验证结果:")
    print(f"  原始记录数: {validation_result['original_count']}")
    print(f"  填补后记录数: {validation_result['filled_count']}")
    print(f"  新增记录数: {validation_result['added_count']}")
    print(f"  时间连续性: {'✓' if validation_result['time_continuity_check'] else '✗'}")
    print(f"  价格连续性: {'✓' if validation_result['price_continuity_check'] else '✗'}")
    print(f"  交易量合理性: {'✓' if validation_result['volume_reasonableness_check'] else '✗'}")
    
    if validation_result['issues']:
        print(f"  发现问题:")
        for issue in validation_result['issues']:
            print(f"    - {issue}")
    else:
        print("  ✓ 数据质量验证通过")
    
    return validation_result

def generate_integrity_report(integrity_result, validation_result, report_file):
    """生成完整性检查报告"""
    print(f"正在生成完整性报告: {report_file}")

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("K线数据完整性检查和填补报告\n")
        f.write("="*50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # 原始数据信息
        f.write("原始数据信息:\n")
        f.write(f"  总记录数: {integrity_result['total_records']}\n")
        f.write(f"  时间范围: {integrity_result['time_range']['start_datetime']} 到 {integrity_result['time_range']['end_datetime']}\n")
        f.write(f"  期望记录数: {integrity_result['expected_total_records']}\n\n")

        # 完整性检查结果
        f.write("完整性检查结果:\n")
        f.write(f"  缺失记录数: {integrity_result['missing_count']}\n")
        f.write(f"  重复记录数: {integrity_result['duplicate_count']}\n")
        f.write(f"  不规则间隔数: {integrity_result['irregular_count']}\n\n")

        # 缺失数据详情
        if integrity_result['missing_intervals']:
            f.write("缺失数据详情:\n")
            for i, missing in enumerate(integrity_result['missing_intervals'][:20]):  # 显示前20个
                f.write(f"  {i+1}. {missing['datetime']} (时间戳: {missing['timestamp']})\n")
            if len(integrity_result['missing_intervals']) > 20:
                f.write(f"  ... 还有 {len(integrity_result['missing_intervals']) - 20} 个缺失点\n")
            f.write("\n")

        # 重复数据详情
        if integrity_result['duplicate_timestamps']:
            f.write("重复时间戳详情:\n")
            for i, dup_timestamp in enumerate(integrity_result['duplicate_timestamps'][:10]):
                f.write(f"  {i+1}. {datetime.fromtimestamp(dup_timestamp)} (时间戳: {dup_timestamp})\n")
            if len(integrity_result['duplicate_timestamps']) > 10:
                f.write(f"  ... 还有 {len(integrity_result['duplicate_timestamps']) - 10} 个重复时间戳\n")
            f.write("\n")

        # 填补结果（如果有）
        if validation_result:
            f.write("数据填补结果:\n")
            f.write(f"  原始记录数: {validation_result['original_count']}\n")
            f.write(f"  填补后记录数: {validation_result['filled_count']}\n")
            f.write(f"  新增记录数: {validation_result['added_count']}\n")
            f.write(f"  时间连续性: {'通过' if validation_result['time_continuity_check'] else '未通过'}\n")
            f.write(f"  价格连续性: {'通过' if validation_result['price_continuity_check'] else '未通过'}\n")
            f.write(f"  交易量合理性: {'通过' if validation_result['volume_reasonableness_check'] else '未通过'}\n\n")

            # 问题列表
            if validation_result['issues']:
                f.write("发现的问题:\n")
                for issue in validation_result['issues']:
                    f.write(f"  - {issue}\n")
            else:
                f.write("✓ 数据质量验证全部通过\n")

        # 建议
        f.write("\n建议:\n")
        if integrity_result['missing_count'] > 0:
            f.write("  - 建议使用智能填补功能修复缺失数据\n")
        if integrity_result['duplicate_count'] > 0:
            f.write("  - 建议移除重复时间戳记录\n")
        if integrity_result['irregular_count'] > 0:
            f.write("  - 检查数据源，确认不规则时间间隔的原因\n")
        if integrity_result['missing_count'] == 0 and integrity_result['duplicate_count'] == 0:
            f.write("  - 数据质量良好，可直接用于分析\n")

    print(f"✓ 完整性报告已保存")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='K线数据完整性检查和智能填补工具')
    parser.add_argument('input_file', help='输入的K线数据CSV文件路径')
    parser.add_argument('--interval', type=int, default=60, help='期望的时间间隔（秒），默认60秒')
    parser.add_argument('--fill-method', choices=['interpolation', 'forward_fill', 'backward_fill', 'average'],
                       default='interpolation', help='填补方法，默认interpolation')
    parser.add_argument('--check-only', action='store_true', help='仅检查完整性，不进行填补')
    parser.add_argument('--output', help='输出文件路径，默认为输入文件名_filled.csv')

    args = parser.parse_args()

    print("="*80)
    print("K线数据完整性检查和智能填补工具")
    print("="*80)

    # 检查输入文件
    if not os.path.exists(args.input_file):
        print(f"错误: 文件 {args.input_file} 不存在")
        sys.exit(1)

    try:
        # 读取数据
        print(f"正在读取文件: {args.input_file}")
        df = pd.read_csv(args.input_file)
        print(f"成功读取 {len(df)} 条记录")

        # 检查必要的列
        required_columns = ['timestamp_sec', 'open', 'high', 'low', 'close', 'vol', 'amount']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"错误: 缺少必要的列: {missing_columns}")
            sys.exit(1)

        # 第一步：移除重复时间戳
        print("\n第一步：移除重复时间戳...")
        df_dedup = remove_duplicate_timestamps(df)

        # 第二步：检查数据完整性
        print("\n第二步：检查数据完整性...")
        integrity_result = check_kline_integrity(df_dedup, args.interval)

        validation_result = None
        df_final = df_dedup

        # 第三步：智能填补（如果需要且不是仅检查模式）
        if not args.check_only and integrity_result['missing_count'] > 0:
            print(f"\n第三步：智能填补缺失数据...")
            df_filled = intelligent_fill_missing_data(df_dedup, integrity_result, args.fill_method)

            # 第四步：验证填补后的数据
            print("\n第四步：验证填补后的数据...")
            validation_result = validate_filled_data(df_dedup, df_filled, args.interval)

            # 保存填补后的数据
            if args.output:
                output_file = args.output
            else:
                base_name = os.path.splitext(args.input_file)[0]
                output_file = f"{base_name}_filled.csv"

            df_filled.to_csv(output_file, index=False)
            print(f"\n✓ 填补后的数据已保存到: {output_file}")
            df_final = df_filled

        # 生成报告
        base_name = os.path.splitext(args.input_file)[0]
        report_file = f"{base_name}_integrity_report.txt"
        generate_integrity_report(integrity_result, validation_result, report_file)

        # 总结
        print(f"\n{'='*80}")
        print("处理完成！")
        print(f"{'='*80}")
        print(f"原始数据: {len(df)} 条记录")
        print(f"去重后数据: {len(df_dedup)} 条记录")
        if validation_result:
            print(f"填补后数据: {validation_result['filled_count']} 条记录")
            print(f"新增记录: {validation_result['added_count']} 条")
        print(f"完整性报告: {report_file}")

        if integrity_result['missing_count'] == 0 and integrity_result['duplicate_count'] == 0:
            print("✓ 数据完整性良好")
        else:
            print(f"⚠ 发现 {integrity_result['missing_count']} 个缺失点，{integrity_result['duplicate_count']} 个重复点")

    except Exception as e:
        print(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
