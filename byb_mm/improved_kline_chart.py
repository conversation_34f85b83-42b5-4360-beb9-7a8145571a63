#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的K线图绘制工具
解决三角形K线问题，提供更自然的K线形态
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def analyze_kline_problems(df):
    """
    分析K线数据中的问题
    """
    print("正在分析K线数据问题...")
    
    # 统计实体大小
    body_sizes = abs(df['close'] - df['open'])
    body_ratios = body_sizes / df['close']
    
    # 统计影线长度
    upper_shadows = df['high'] - df[['open', 'close']].max(axis=1)
    lower_shadows = df[['open', 'close']].min(axis=1) - df['low']
    
    # 统计波动率
    volatilities = (df['high'] / df['low'] - 1) * 100
    
    print(f"K线形态分析:")
    print(f"  平均实体大小: {body_sizes.mean():.8f} USDT ({body_ratios.mean()*100:.4f}%)")
    print(f"  最小实体大小: {body_sizes.min():.8f} USDT")
    print(f"  实体为零的K线: {(body_sizes == 0).sum()} 根 ({(body_sizes == 0).sum()/len(df)*100:.1f}%)")
    print(f"  平均上影线: {upper_shadows.mean():.8f} USDT")
    print(f"  平均下影线: {lower_shadows.mean():.8f} USDT")
    print(f"  平均波动率: {volatilities.mean():.4f}%")
    print(f"  最大波动率: {volatilities.max():.4f}%")
    
    # 检测三角形K线（实体很小，影线相对较长）
    tiny_body_threshold = body_ratios.quantile(0.1)  # 最小10%的实体
    triangle_klines = (body_ratios < tiny_body_threshold) & ((upper_shadows + lower_shadows) > body_sizes * 3)
    
    print(f"  疑似三角形K线: {triangle_klines.sum()} 根 ({triangle_klines.sum()/len(df)*100:.1f}%)")
    
    return {
        'body_sizes': body_sizes,
        'body_ratios': body_ratios,
        'upper_shadows': upper_shadows,
        'lower_shadows': lower_shadows,
        'volatilities': volatilities,
        'triangle_klines': triangle_klines
    }

def improve_kline_shape(df, min_body_ratio=0.0001, max_volatility=0.03, natural_ratio=0.7):
    """
    改进K线形态，减少三角形K线
    Args:
        df: K线数据
        min_body_ratio: 最小实体比例（相对于价格）
        max_volatility: 最大波动率
        natural_ratio: 自然形态保持比例
    """
    print(f"正在改进K线形态...")
    print(f"参数设置: 最小实体比例={min_body_ratio*100:.2f}%, 最大波动率={max_volatility*100:.1f}%, 自然比例={natural_ratio*100:.0f}%")
    
    df_improved = df.copy()
    improvement_count = 0
    
    for i in range(len(df_improved)):
        open_price = df_improved['open'].iloc[i]
        close_price = df_improved['close'].iloc[i]
        high_price = df_improved['high'].iloc[i]
        low_price = df_improved['low'].iloc[i]
        
        # 计算当前实体大小
        current_body = abs(close_price - open_price)
        current_body_ratio = current_body / close_price if close_price > 0 else 0
        
        # 计算当前波动率
        current_volatility = (high_price / low_price - 1) if low_price > 0 else 0
        
        # 如果实体太小，适当增加实体大小
        if current_body_ratio < min_body_ratio:
            # 计算目标实体大小
            target_body = close_price * min_body_ratio
            
            # 保持原有的涨跌方向
            if close_price >= open_price:
                # 上涨K线
                new_close = open_price + target_body * natural_ratio
                new_open = open_price
            else:
                # 下跌K线
                new_close = open_price - target_body * natural_ratio
                new_open = open_price
            
            # 更新开盘收盘价
            df_improved.iloc[i, df_improved.columns.get_loc('open')] = new_open
            df_improved.iloc[i, df_improved.columns.get_loc('close')] = new_close
            
            # 重新计算高低价
            body_center = (new_open + new_close) / 2
            body_range = abs(new_close - new_open)
            
            # 保持合理的影线长度（不超过实体的2倍）
            max_shadow = body_range * 2
            
            new_high = max(new_open, new_close) + max_shadow * 0.5
            new_low = min(new_open, new_close) - max_shadow * 0.5
            
            # 确保不超过原始的高低价范围（保持数据真实性）
            new_high = min(new_high, high_price)
            new_low = max(new_low, low_price)
            
            df_improved.iloc[i, df_improved.columns.get_loc('high')] = new_high
            df_improved.iloc[i, df_improved.columns.get_loc('low')] = new_low
            
            improvement_count += 1
        
        # 如果波动率过大，适当压缩
        elif current_volatility > max_volatility:
            # 以开盘收盘价的中点为基准，压缩高低价
            oc_center = (open_price + close_price) / 2
            oc_range = abs(close_price - open_price)
            
            # 计算允许的最大价格范围
            max_range = oc_center * max_volatility
            
            # 确保包含开盘收盘价
            min_required_high = max(open_price, close_price)
            max_required_low = min(open_price, close_price)
            
            # 计算新的高低价
            new_high = min(high_price, oc_center + max_range / 2)
            new_low = max(low_price, oc_center - max_range / 2)
            
            # 确保包含开盘收盘价
            new_high = max(new_high, min_required_high)
            new_low = min(new_low, max_required_low)
            
            df_improved.iloc[i, df_improved.columns.get_loc('high')] = new_high
            df_improved.iloc[i, df_improved.columns.get_loc('low')] = new_low
            
            improvement_count += 1
    
    print(f"✓ 改进了 {improvement_count} 根K线的形态")
    return df_improved

def plot_improved_candlestick_chart(df, title="BYBUSDT K线图（改进版）", timeframe="1分钟", 
                                  improve_shape=True, show_comparison=True):
    """
    绘制改进的K线图
    Args:
        df: K线数据DataFrame
        title: 图表标题
        timeframe: 时间周期
        improve_shape: 是否改进K线形态
        show_comparison: 是否显示对比
    """
    print(f"正在绘制改进的K线图...")
    
    # 数据预处理
    df_plot = df.copy()
    df_plot['datetime'] = pd.to_datetime(df_plot['timestamp_sec'], unit='s')
    
    # 分析原始数据问题
    original_analysis = analyze_kline_problems(df_plot)
    
    # 改进K线形态
    if improve_shape:
        df_improved = improve_kline_shape(df_plot, min_body_ratio=0.0002, max_volatility=0.05, natural_ratio=0.8)
        improved_analysis = analyze_kline_problems(df_improved)
    else:
        df_improved = df_plot.copy()
        improved_analysis = original_analysis
    
    # 创建图表
    if show_comparison and improve_shape:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16),
                                                    gridspec_kw={'height_ratios': [3, 1]})
        axes_pairs = [(ax1, ax3, df_plot, "原始K线"), (ax2, ax4, df_improved, "改进K线")]
    else:
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12),
                                      gridspec_kw={'height_ratios': [3, 1]})
        axes_pairs = [(ax1, ax2, df_improved, "K线图")]
    
    for ax_main, ax_vol, data, subtitle in axes_pairs:
        # 绘制K线
        for i in range(len(data)):
            row = data.iloc[i]
            x = row['datetime']
            open_price = row['open']
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']
            
            # 确定颜色：红涨绿跌
            color = 'red' if close_price >= open_price else 'green'
            
            # 绘制影线
            ax_main.plot([x, x], [low_price, high_price], color='black', linewidth=0.8)
            
            # 绘制实体
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)
            
            if body_height > 0:
                # 调整K线宽度，使其更清晰
                width = 0.0008 if show_comparison else 0.0006
                rect = Rectangle((mdates.date2num(x) - width/2, body_bottom),
                               width, body_height,
                               facecolor=color, edgecolor='black', linewidth=0.5, alpha=0.8)
                ax_main.add_patch(rect)
            else:
                # 十字星
                width = 0.0008 if show_comparison else 0.0006
                ax_main.plot([mdates.date2num(x) - width/2, mdates.date2num(x) + width/2],
                           [open_price, open_price], color='black', linewidth=1.5)
        
        # 设置主图
        full_title = f"{title} - {subtitle} ({timeframe})"
        ax_main.set_title(full_title, fontsize=14, fontweight='bold', pad=15)
        ax_main.set_ylabel('价格 (USDT)', fontsize=12)
        ax_main.grid(True, alpha=0.3)
        ax_main.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax_main.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        
        # 添加统计信息
        if subtitle == "原始K线":
            stats = original_analysis
        else:
            stats = improved_analysis
            
        stats_text = f"实体均值: {stats['body_ratios'].mean()*100:.3f}% | " \
                    f"三角形: {stats['triangle_klines'].sum()}根 | " \
                    f"波动率: {stats['volatilities'].mean():.2f}%"
        
        ax_main.text(0.02, 0.98, stats_text, transform=ax_main.transAxes,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
                    verticalalignment='top', fontsize=9)
        
        # 绘制交易量
        colors = ['red' if data.iloc[i]['close'] >= data.iloc[i]['open']
                 else 'green' for i in range(len(data))]
        
        bar_width = pd.Timedelta(minutes=0.8) if show_comparison else pd.Timedelta(minutes=0.8)
        ax_vol.bar(data['datetime'], data['vol'],
                  color=colors, alpha=0.7, width=bar_width)
        
        ax_vol.set_ylabel('交易量 (BYB)', fontsize=10)
        ax_vol.grid(True, alpha=0.3)
        ax_vol.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax_vol.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        
        # 旋转x轴标签
        plt.setp(ax_main.xaxis.get_majorticklabels(), rotation=45)
        plt.setp(ax_vol.xaxis.get_majorticklabels(), rotation=45)
    
    # 设置x轴标签
    if show_comparison and improve_shape:
        ax3.set_xlabel('时间', fontsize=12)
        ax4.set_xlabel('时间', fontsize=12)
    else:
        ax2.set_xlabel('时间', fontsize=12)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    timeframe_safe = timeframe.replace(':', '').replace('/', '')
    suffix = "_improved_comparison" if show_comparison and improve_shape else "_improved"
    filename = f'bybusdt_{timeframe_safe}_kline_chart{suffix}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"改进K线图已保存为: {filename}")
    
    # 显示图表
    plt.show()
    
    # 输出改进效果统计
    if improve_shape:
        print(f"\n=== K线形态改进效果 ===")
        print(f"原始数据:")
        print(f"  三角形K线: {original_analysis['triangle_klines'].sum()} 根 ({original_analysis['triangle_klines'].sum()/len(df)*100:.1f}%)")
        print(f"  平均实体比例: {original_analysis['body_ratios'].mean()*100:.4f}%")
        print(f"  平均波动率: {original_analysis['volatilities'].mean():.4f}%")
        
        print(f"改进后:")
        print(f"  三角形K线: {improved_analysis['triangle_klines'].sum()} 根 ({improved_analysis['triangle_klines'].sum()/len(df)*100:.1f}%)")
        print(f"  平均实体比例: {improved_analysis['body_ratios'].mean()*100:.4f}%")
        print(f"  平均波动率: {improved_analysis['volatilities'].mean():.4f}%")
        
        triangle_reduction = original_analysis['triangle_klines'].sum() - improved_analysis['triangle_klines'].sum()
        print(f"  三角形K线减少: {triangle_reduction} 根")
    
    return df_improved if improve_shape else df_plot

def main():
    """主函数"""
    print("="*80)
    print("改进的K线图绘制工具")
    print("="*80)
    
    # 读取K线数据
    try:
        # 尝试读取完整的K线数据
        df = pd.read_csv('bybusdt_1min_kline_2025-07-03_complete.csv')
        print(f"成功读取完整K线数据: {len(df)} 条记录")
    except FileNotFoundError:
        try:
            # 如果没有完整数据，读取原始数据
            df = pd.read_csv('bybusdt_1min_kline_2025-07-03.csv')
            print(f"成功读取原始K线数据: {len(df)} 条记录")
        except FileNotFoundError:
            print("错误: 未找到K线数据文件")
            return
    
    # 绘制改进的K线图
    df_improved = plot_improved_candlestick_chart(
        df, 
        title="BYBUSDT K线图", 
        timeframe="1分钟",
        improve_shape=True,
        show_comparison=True
    )
    
    # 保存改进后的数据
    output_file = 'bybusdt_1min_kline_improved_shape.csv'
    df_improved.to_csv(output_file, index=False)
    print(f"\n改进后的K线数据已保存到: {output_file}")

if __name__ == "__main__":
    main()
